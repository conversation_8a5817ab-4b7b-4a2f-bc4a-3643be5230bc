{% doc %}
  @prompt
    create a section in which we have two divs, left div will contain image slider and right div contain heading and text option.
    Image slider should have an option to add image, title and description of the image.. title and description will appear on the image bottom with blur background., images are cutting..

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-image-text-section-{{ ai_gen_id }} {
    display: flex;
    gap: 40px;
    align-items: center;
    padding: 60px 0;
    background-color: {{ block.settings.background_color }};
  }

  .ai-image-slider-container-{{ ai_gen_id }} {
    flex: 1;
    position: relative;
    max-width: 600px;
  }

  .ai-slider-wrapper-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
    border-radius: {{ block.settings.image_border_radius }}px;
    width: 100%;
    height: 500px;
  }

  .ai-slider-track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%;
  }

  .ai-slide-{{ ai_gen_id }} {
    min-width: 100%;
    position: relative;
    height: 100%;
    flex-shrink: 0;
  }

  .ai-slide-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: {{ block.settings.image_fit }};
    display: block;
  }

  .ai-slide-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-slide-placeholder-{{ ai_gen_id }} svg {
    width: 60%;
    height: 60%;
    opacity: 0.3;
  }

  .ai-slide-content-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    color: white;
  }

  .ai-slide-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.slide_title_size }}px;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .ai-slide-description-{{ ai_gen_id }} {
    font-size: {{ block.settings.slide_description_size }}px;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }

  .ai-slider-nav-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 2;
  }

  .ai-slider-nav-{{ ai_gen_id }}:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
  }

  .ai-slider-nav-{{ ai_gen_id }}:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .ai-slider-nav-{{ ai_gen_id }}:disabled:hover {
    transform: translateY(-50%) scale(1);
  }

  .ai-slider-prev-{{ ai_gen_id }} {
    left: 16px;
  }

  .ai-slider-next-{{ ai_gen_id }} {
    right: 16px;
  }

  .ai-slider-dots-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
  }

  .ai-slider-dot-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .ai-slider-dot-{{ ai_gen_id }}.active {
    background: {{ block.settings.accent_color }};
    transform: scale(1.2);
  }

  .ai-content-section-{{ ai_gen_id }} {
    flex: 1;
    padding-left: 20px;
    text-align:right;
    max-width: 47%;
    margin: 0 auto;
  }

  .ai-content-heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    color: {{ block.settings.heading_color }};
    margin: 0 0 20px 0;
    line-height: 1.2;
    font-weight: 600;
  }

  .ai-content-text-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.6;
    margin: 0;
  }

  @media screen and (max-width: 768px) {
    .ai-image-text-section-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 30px;
      padding: 40px 0;
    }

    .ai-slider-wrapper-{{ ai_gen_id }} {
      height: 300px;
    }

    .ai-content-section-{{ ai_gen_id }} {
      padding-left: 0;
    }

    .ai-slide-content-{{ ai_gen_id }} {
      padding: 16px;
    }

    .ai-slide-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.slide_title_size | times: 0.9 }}px;
    }

    .ai-slide-description-{{ ai_gen_id }} {
      font-size: {{ block.settings.slide_description_size | times: 0.9 }}px;
    }

    .ai-content-heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.8 }}px;
    }

    .ai-content-text-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<image-text-slider-{{ ai_gen_id }}
  class="ai-image-text-section-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-image-slider-container-{{ ai_gen_id }}">
    <div class="ai-slider-wrapper-{{ ai_gen_id }}">
      <div class="ai-slider-track-{{ ai_gen_id }}" data-slider-track>
        {% for i in (1..5) %}
          {% liquid
            assign image_key = 'slide_' | append: i | append: '_image'
            assign title_key = 'slide_' | append: i | append: '_title'
            assign description_key = 'slide_' | append: i | append: '_description'
            
            assign slide_image = block.settings[image_key]
            assign slide_title = block.settings[title_key]
            assign slide_description = block.settings[description_key]
          %}
          
          {% if slide_image != blank or slide_title != blank or slide_description != blank %}
            <div class="ai-slide-{{ ai_gen_id }}" data-slide="{{ forloop.index0 }}">
              {% if slide_image %}
                <img
                  src="{{ slide_image | image_url: width: 800 }}"
                  alt="{{ slide_image.alt | escape }}"
                  class="ai-slide-image-{{ ai_gen_id }}"
                  loading="lazy"
                  width="{{ slide_image.width }}"
                  height="{{ slide_image.height }}"
                >
              {% else %}
                <div class="ai-slide-placeholder-{{ ai_gen_id }}">
                  {{ 'image' | placeholder_svg_tag }}
                </div>
              {% endif %}
              
              {% if slide_title != blank or slide_description != blank %}
                <div class="ai-slide-content-{{ ai_gen_id }}">
                  {% if slide_title != blank %}
                    <h3 class="ai-slide-title-{{ ai_gen_id }}">{{ slide_title }}</h3>
                  {% endif %}
                  {% if slide_description != blank %}
                    <p class="ai-slide-description-{{ ai_gen_id }}">{{ slide_description }}</p>
                  {% endif %}
                </div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <button
        class="ai-slider-nav-{{ ai_gen_id }} ai-slider-prev-{{ ai_gen_id }}"
        data-slider-prev
        aria-label="Previous slide"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>
      
      <button
        class="ai-slider-nav-{{ ai_gen_id }} ai-slider-next-{{ ai_gen_id }}"
        data-slider-next
        aria-label="Next slide"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    </div>
    
    <div class="ai-slider-dots-{{ ai_gen_id }}" data-slider-dots></div>
  </div>

  <div class="ai-content-section-{{ ai_gen_id }}">
    {% if block.settings.heading != blank %}
      <h2 class="ai-content-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
    {% endif %}
    {% if block.settings.text != blank %}
      <div class="ai-content-text-{{ ai_gen_id }}">{{ block.settings.text }}</div>
    {% endif %}

    <!-- ВИДИМЫЙ ТЕКСТ: ИСПОЛЬЗУЕТСЯ AI ИЗОБРАЖЕНИЕ-ТЕКСТ СЛАЙДЕР -->
    <div style="background: #e74c3c; color: white; padding: 8px; text-align: center; font-weight: bold; margin: 10px 0; border-radius: 5px; font-size: 14px;">
      🖼️ ЗАГРУЖЕН AI ИЗОБРАЖЕНИЕ-ТЕКСТ СЛАЙДЕР: blocks/ai_gen_block_c70b27a.liquid
    </div>
  </div>
</image-text-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class ImageTextSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.currentSlide = 0;
        this.slides = [];
        this.totalSlides = 0;
      }

      connectedCallback() {
        this.track = this.querySelector('[data-slider-track]');
        this.prevBtn = this.querySelector('[data-slider-prev]');
        this.nextBtn = this.querySelector('[data-slider-next]');
        this.dotsContainer = this.querySelector('[data-slider-dots]');
        
        this.slides = Array.from(this.querySelectorAll('.ai-slide-{{ ai_gen_id }}'));
        this.totalSlides = this.slides.length;
        
        if (this.totalSlides <= 1) {
          this.prevBtn.style.display = 'none';
          this.nextBtn.style.display = 'none';
          this.dotsContainer.style.display = 'none';
          return;
        }
        
        this.createDots();
        this.updateSlider();
        this.setupEventListeners();
      }

      createDots() {
        this.dotsContainer.innerHTML = '';
        for (let i = 0; i < this.totalSlides; i++) {
          const dot = document.createElement('button');
          dot.className = 'ai-slider-dot-{{ ai_gen_id }}';
          dot.setAttribute('data-slide', i);
          dot.setAttribute('aria-label', `Go to slide ${i + 1}`);
          this.dotsContainer.appendChild(dot);
        }
      }

      setupEventListeners() {
        this.prevBtn.addEventListener('click', () => this.prevSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());
        
        this.dotsContainer.addEventListener('click', (e) => {
          if (e.target.hasAttribute('data-slide')) {
            this.goToSlide(parseInt(e.target.getAttribute('data-slide')));
          }
        });
      }

      prevSlide() {
        this.currentSlide = this.currentSlide === 0 ? this.totalSlides - 1 : this.currentSlide - 1;
        this.updateSlider();
      }

      nextSlide() {
        this.currentSlide = this.currentSlide === this.totalSlides - 1 ? 0 : this.currentSlide + 1;
        this.updateSlider();
      }

      goToSlide(index) {
        this.currentSlide = index;
        this.updateSlider();
      }

      updateSlider() {
        const translateX = -this.currentSlide * 100;
        this.track.style.transform = `translateX(${translateX}%)`;
        
        this.querySelectorAll('.ai-slider-dot-{{ ai_gen_id }}').forEach((dot, index) => {
          if (dot && dot.classList) {
            dot.classList.toggle('active', index === this.currentSlide);
          }
        });
        
        this.prevBtn.disabled = this.currentSlide === 0;
        this.nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
      }
    }

    customElements.define('image-text-slider-{{ ai_gen_id }}', ImageTextSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Image Text Slider",
  "settings": [
    {
      "type": "header",
      "content": "Content Section"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Our Story"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Discover our journey through these beautiful moments captured in time. Each image tells a unique story of our passion and dedication.</p>"
    },
    {
      "type": "header",
      "content": "Slider Images"
    },
    {
      "type": "image_picker",
      "id": "slide_1_image",
      "label": "Slide 1 - Image"
    },
    {
      "type": "text",
      "id": "slide_1_title",
      "label": "Slide 1 - Title",
      "default": "Beautiful Moment"
    },
    {
      "type": "textarea",
      "id": "slide_1_description",
      "label": "Slide 1 - Description",
      "default": "A stunning capture of nature's beauty"
    },
    {
      "type": "image_picker",
      "id": "slide_2_image",
      "label": "Slide 2 - Image"
    },
    {
      "type": "text",
      "id": "slide_2_title",
      "label": "Slide 2 - Title"
    },
    {
      "type": "textarea",
      "id": "slide_2_description",
      "label": "Slide 2 - Description"
    },
    {
      "type": "image_picker",
      "id": "slide_3_image",
      "label": "Slide 3 - Image"
    },
    {
      "type": "text",
      "id": "slide_3_title",
      "label": "Slide 3 - Title"
    },
    {
      "type": "textarea",
      "id": "slide_3_description",
      "label": "Slide 3 - Description"
    },
    {
      "type": "image_picker",
      "id": "slide_4_image",
      "label": "Slide 4 - Image"
    },
    {
      "type": "text",
      "id": "slide_4_title",
      "label": "Slide 4 - Title"
    },
    {
      "type": "textarea",
      "id": "slide_4_description",
      "label": "Slide 4 - Description"
    },
    {
      "type": "image_picker",
      "id": "slide_5_image",
      "label": "Slide 5 - Image"
    },
    {
      "type": "text",
      "id": "slide_5_title",
      "label": "Slide 5 - Title"
    },
    {
      "type": "textarea",
      "id": "slide_5_description",
      "label": "Slide 5 - Description"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "range",
      "id": "slider_height",
      "min": 200,
      "max": 600,
      "step": 20,
      "unit": "px",
      "label": "Slider height",
      "default": 400
    },
    {
      "type": "select",
      "id": "image_fit",
      "label": "Image fit",
      "options": [
        {
          "value": "cover",
          "label": "Cover"
        },
        {
          "value": "contain",
          "label": "Contain"
        },
        {
          "value": "fill",
          "label": "Fill"
        }
      ],
      "default": "cover"
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Image border radius",
      "default": 12
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 36
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "slide_title_size",
      "min": 14,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Slide title size",
      "default": 20
    },
    {
      "type": "range",
      "id": "slide_description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Slide description size",
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Image Text Slider"
    }
  ]
}
{% endschema %}