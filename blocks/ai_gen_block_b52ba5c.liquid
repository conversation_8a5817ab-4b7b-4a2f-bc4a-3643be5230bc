{% doc %}
  @prompt
    Create a video slider with html video  

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-slider-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    max-width: 100%;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
    overflow: hidden;
  }

  .ai-video-slider__container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: {{ block.settings.slider_height }}px;
    overflow: hidden;
  }

  .ai-video-slider__track-{{ ai_gen_id }} {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
  }

  .ai-video-slider__slide-{{ ai_gen_id }} {
    flex: 0 0 100%;
    width: 100%;
    height: 100%;
    position: relative;
  }

  .ai-video-slider__video-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .ai-video-slider__placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 18px;
  }

  .ai-video-slider__overlay-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 30px;
    color: white;
  }

  .ai-video-slider__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .ai-video-slider__description-{{ ai_gen_id }} {
    font-size: {{ block.settings.description_size }}px;
    margin: 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .ai-video-slider__controls-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  .ai-video-slider__control-{{ ai_gen_id }} {
    background-color: {{ block.settings.control_color }};
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: {{ block.settings.control_icon_color }};
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .ai-video-slider__control-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.control_hover_color }};
    transform: scale(1.1);
  }

  .ai-video-slider__control-{{ ai_gen_id }}:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .ai-video-slider__control--prev-{{ ai_gen_id }} {
    left: 20px;
  }

  .ai-video-slider__control--next-{{ ai_gen_id }} {
    right: 20px;
  }

  .ai-video-slider__control-{{ ai_gen_id }} svg {
    width: 20px;
    height: 20px;
  }

  .ai-video-slider__dots-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 20px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
  }

  .ai-video-slider__dot-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .ai-video-slider__dot-{{ ai_gen_id }}.active {
    background-color: {{ block.settings.dot_active_color }};
    transform: scale(1.2);
  }

  .ai-video-slider__play-pause-{{ ai_gen_id }} {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: {{ block.settings.control_color }};
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: {{ block.settings.control_icon_color }};
    z-index: 10;
  }

  .ai-video-slider__play-pause-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.control_hover_color }};
    transform: scale(1.1);
  }

  .ai-video-slider__play-pause-{{ ai_gen_id }} svg {
    width: 16px;
    height: 16px;
  }

  @media screen and (max-width: 749px) {
    .ai-video-slider__container-{{ ai_gen_id }} {
      height: {{ block.settings.slider_height | times: 0.7 }}px;
    }

    .ai-video-slider__overlay-{{ ai_gen_id }} {
      padding: 20px;
    }

    .ai-video-slider__title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_size | times: 0.8 }}px;
    }

    .ai-video-slider__description-{{ ai_gen_id }} {
      font-size: {{ block.settings.description_size | times: 0.8 }}px;
    }

    .ai-video-slider__control-{{ ai_gen_id }} {
      width: 40px;
      height: 40px;
    }

    .ai-video-slider__control--prev-{{ ai_gen_id }} {
      left: 10px;
    }

    .ai-video-slider__control--next-{{ ai_gen_id }} {
      right: 10px;
    }
  }
{% endstyle %}

<video-slider-{{ ai_gen_id }}
  class="ai-video-slider-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-video-slider__container-{{ ai_gen_id }}">
    <div class="ai-video-slider__track-{{ ai_gen_id }}">
      {% for i in (1..5) %}
        {% liquid
          assign video_key = 'video_' | append: i
          assign title_key = 'video_' | append: i | append: '_title'
          assign description_key = 'video_' | append: i | append: '_description'
          
          assign video = block.settings[video_key]
          assign title = block.settings[title_key]
          assign description = block.settings[description_key]
        %}

        {% if video != blank or i == 1 %}
          <div class="ai-video-slider__slide-{{ ai_gen_id }}" data-slide="{{ forloop.index0 }}">
            {% if video != blank %}
              <video
                class="ai-video-slider__video-{{ ai_gen_id }}"
                muted
                loop
                playsinline
                preload="metadata"
              >
                <source src="{{ video }}" type="video/mp4">
                Your browser does not support the video tag.
              </video>
            {% else %}
              <div class="ai-video-slider__placeholder-{{ ai_gen_id }}">
                Video {{ i }}
              </div>
            {% endif %}

            {% if title != blank or description != blank %}
              <div class="ai-video-slider__overlay-{{ ai_gen_id }}">
                {% if title != blank %}
                  <h3 class="ai-video-slider__title-{{ ai_gen_id }}">{{ title }}</h3>
                {% endif %}
                {% if description != blank %}
                  <p class="ai-video-slider__description-{{ ai_gen_id }}">{{ description }}</p>
                {% endif %}
              </div>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>

    {% if block.settings.show_controls %}
      <button
        class="ai-video-slider__controls-{{ ai_gen_id }} ai-video-slider__control-{{ ai_gen_id }} ai-video-slider__control--prev-{{ ai_gen_id }}"
        aria-label="Previous video"
        data-direction="prev"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>

      <button
        class="ai-video-slider__controls-{{ ai_gen_id }} ai-video-slider__control-{{ ai_gen_id }} ai-video-slider__control--next-{{ ai_gen_id }}"
        aria-label="Next video"
        data-direction="next"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    {% endif %}

    {% if block.settings.show_play_pause %}
      <button
        class="ai-video-slider__play-pause-{{ ai_gen_id }}"
        aria-label="Play/Pause video"
      >
        <svg class="play-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polygon points="5,3 19,12 5,21"></polygon>
        </svg>
        <svg class="pause-icon" style="display: none;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="6" y="4" width="4" height="16"></rect>
          <rect x="14" y="4" width="4" height="16"></rect>
        </svg>
      </button>
    {% endif %}

    {% if block.settings.show_dots %}
      <div class="ai-video-slider__dots-{{ ai_gen_id }}">
        {% for i in (1..5) %}
          {% liquid
            assign video_key = 'video_' | append: i
            assign video = block.settings[video_key]
          %}
          {% if video != blank or i == 1 %}
            <button
              class="ai-video-slider__dot-{{ ai_gen_id }} {% if forloop.first %}active{% endif %}"
              aria-label="Go to video {{ i }}"
              data-slide="{{ forloop.index0 }}"
            ></button>
          {% endif %}
        {% endfor %}
      </div>
    {% endif %}
  </div>
</video-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class VideoSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.currentSlide = 0;
        this.slides = [];
        this.videos = [];
        this.isPlaying = {{ block.settings.autoplay | json }};
        this.autoplayInterval = null;
      }

      connectedCallback() {
        this.track = this.querySelector('.ai-video-slider__track-{{ ai_gen_id }}');
        this.slides = Array.from(this.querySelectorAll('.ai-video-slider__slide-{{ ai_gen_id }}'));
        this.videos = Array.from(this.querySelectorAll('.ai-video-slider__video-{{ ai_gen_id }}'));
        this.dots = Array.from(this.querySelectorAll('.ai-video-slider__dot-{{ ai_gen_id }}'));
        this.prevButton = this.querySelector('.ai-video-slider__control--prev-{{ ai_gen_id }}');
        this.nextButton = this.querySelector('.ai-video-slider__control--next-{{ ai_gen_id }}');
        this.playPauseButton = this.querySelector('.ai-video-slider__play-pause-{{ ai_gen_id }}');

        this.setupEventListeners();
        this.updateSlider();
        
        if (this.isPlaying) {
          this.startAutoplay();
          this.playCurrentVideo();
        }
      }

      setupEventListeners() {
        if (this.prevButton) {
          this.prevButton.addEventListener('click', () => this.previousSlide());
        }

        if (this.nextButton) {
          this.nextButton.addEventListener('click', () => this.nextSlide());
        }

        if (this.playPauseButton) {
          this.playPauseButton.addEventListener('click', () => this.togglePlayPause());
        }

        this.dots.forEach((dot, index) => {
          dot.addEventListener('click', () => this.goToSlide(index));
        });

        this.videos.forEach((video, index) => {
          video.addEventListener('ended', () => {
            if (index === this.currentSlide) {
              this.nextSlide();
            }
          });
        });

        this.addEventListener('mouseenter', () => this.pauseAutoplay());
        this.addEventListener('mouseleave', () => {
          if (this.isPlaying) {
            this.startAutoplay();
          }
        });
      }

      updateSlider() {
        const translateX = -this.currentSlide * 100;
        this.track.style.transform = `translateX(${translateX}%)`;

        this.dots.forEach((dot, index) => {
          if (dot && dot.classList) {
            dot.classList.toggle('active', index === this.currentSlide);
          }
        });

        if (this.prevButton) {
          this.prevButton.disabled = this.currentSlide === 0;
        }

        if (this.nextButton) {
          this.nextButton.disabled = this.currentSlide === this.slides.length - 1;
        }

        this.pauseAllVideos();
        if (this.isPlaying) {
          this.playCurrentVideo();
        }
      }

      nextSlide() {
        if (this.currentSlide < this.slides.length - 1) {
          this.currentSlide++;
        } else {
          this.currentSlide = 0;
        }
        this.updateSlider();
      }

      previousSlide() {
        if (this.currentSlide > 0) {
          this.currentSlide--;
        } else {
          this.currentSlide = this.slides.length - 1;
        }
        this.updateSlider();
      }

      goToSlide(index) {
        this.currentSlide = index;
        this.updateSlider();
      }

      togglePlayPause() {
        this.isPlaying = !this.isPlaying;
        
        const playIcon = this.playPauseButton.querySelector('.play-icon');
        const pauseIcon = this.playPauseButton.querySelector('.pause-icon');
        
        if (this.isPlaying) {
          playIcon.style.display = 'none';
          pauseIcon.style.display = 'block';
          this.playCurrentVideo();
          this.startAutoplay();
        } else {
          playIcon.style.display = 'block';
          pauseIcon.style.display = 'none';
          this.pauseAllVideos();
          this.pauseAutoplay();
        }
      }

      playCurrentVideo() {
        const currentVideo = this.videos[this.currentSlide];
        if (currentVideo) {
          currentVideo.play().catch(() => {});
        }
      }

      pauseAllVideos() {
        this.videos.forEach(video => {
          video.pause();
        });
      }

      startAutoplay() {
        this.pauseAutoplay();
        if ({{ block.settings.autoplay_speed }} > 0) {
          this.autoplayInterval = setInterval(() => {
            this.nextSlide();
          }, {{ block.settings.autoplay_speed }} * 1000);
        }
      }

      pauseAutoplay() {
        if (this.autoplayInterval) {
          clearInterval(this.autoplayInterval);
          this.autoplayInterval = null;
        }
      }

      disconnectedCallback() {
        this.pauseAutoplay();
      }
    }

    customElements.define('video-slider-{{ ai_gen_id }}', VideoSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Video Slider",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "slider_height",
      "min": 300,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Slider height",
      "default": 500
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Controls"
    },
    {
      "type": "checkbox",
      "id": "show_controls",
      "label": "Show navigation arrows",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots navigation",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_play_pause",
      "label": "Show play/pause button",
      "default": true
    },
    {
      "type": "color",
      "id": "control_color",
      "label": "Control background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "control_hover_color",
      "label": "Control hover color",
      "default": "#f0f0f0"
    },
    {
      "type": "color",
      "id": "control_icon_color",
      "label": "Control icon color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "dot_active_color",
      "label": "Active dot color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Autoplay"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "Autoplay speed",
      "default": 5
    },
    {
      "type": "header",
      "content": "Text Overlay"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 28
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Video 1"
    },
    {
      "type": "text",
      "id": "video_1",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "video_1_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "video_1_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video 2"
    },
    {
      "type": "text",
      "id": "video_2",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "video_2_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "video_2_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video 3"
    },
    {
      "type": "text",
      "id": "video_3",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "video_3_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "video_3_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video 4"
    },
    {
      "type": "text",
      "id": "video_4",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "video_4_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "video_4_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video 5"
    },
    {
      "type": "text",
      "id": "video_5",
      "label": "Video URL"
    },
    {
      "type": "text",
      "id": "video_5_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "video_5_description",
      "label": "Description"
    }
  ],
  "presets": [
    {
      "name": "Video Slider"
    }
  ]
}
{% endschema %}